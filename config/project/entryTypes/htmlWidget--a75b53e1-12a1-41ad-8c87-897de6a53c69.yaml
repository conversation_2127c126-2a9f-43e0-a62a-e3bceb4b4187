color: null
fieldLayouts:
  de66be33-48be-4c04-94c3-f5fec4e4d3b1:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-02-05T03:46:33+00:00'
            elementCondition: null
            fieldUid: 0214b58e-2bf0-414a-bb51-1950513efbe4 # HTML Widget
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7b063674-4c3d-4872-81c0-75beb3273133
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-12T01:29:55+00:00'
            elementCondition: null
            fieldUid: c8b583af-2579-4a45-b50c-a9d37005074a # Panel Width
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: 'Only applicable on pages without a sidebar'
            type: craft\fieldlayoutelements\CustomField
            uid: 2b38ba61-ff50-414c-9bd1-61b95a1ea204
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-05-15T05:47:15+00:00'
            elementCondition: null
            fieldUid: 465145dd-3bd1-4710-927e-28ed6b4aca6e # Background Colour
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f3a59601-44e2-4ea3-9fa2-498151c79920
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 838afd8e-02f8-4b50-8fab-45cf528b89b4
        userCondition: null
handle: htmlWidget
hasTitleField: false
icon: cube
name: 'HTML / Widget'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
