color: green
fieldLayouts:
  4aeabf4a-680a-43c3-9a3d-c94fa8f0438a:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-02-12T23:06:40+00:00'
            elementCondition: null
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 53891c09-7cfa-4c32-9f66-639d6457a2b5
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-02-12T23:07:17+00:00'
            elementCondition: null
            fieldUid: 89303a1b-8fec-4e93-9b39-9b5d6f2d4f22 # Excerpt
            handle: tagline
            includeInCards: false
            instructions: null
            label: Tagline
            providesThumbs: false
            required: false
            tip: 'If you have a title that''s more than a couple words long, the layout may look best *without* a tagline.'
            type: craft\fieldlayoutelements\CustomField
            uid: 115750a0-4015-46df-8528-8e59eea5e080
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-02-12T23:17:24+00:00'
            dismissible: false
            elementCondition: null
            style: tip
            tip: 'The testimonials slider will randomly select up to 6 testimonials. '
            type: craft\fieldlayoutelements\Tip
            uid: 11328d6b-2f10-4e11-aec3-34bc56fb261a
            userCondition: null
          -
            content: "The footer testimonial slider will appear on all pages as part of the footer. \r\nIf you'd like the slider to only appear on a homepage, create a testimonial slider within the home section and ensure there is no testimonial slider active/existing within the footer section. \r\n\r\nIf the slider is on an *accommodation* page, it will try to find any testimonials where the **Stayed At** field value matches the current accommodation page. If there are fewer than six testimonials that match that accommodation, the slider will randomly select additional testimonials till there are six. \r\n\r\nA note on an *all reviews* or *all testimonials* page: using the *Testimonial Gallery* page add-on is recommended as it will display all testimonials in a grid. "
            dateAdded: '2025-02-12T23:32:01+00:00'
            displayInPane: true
            elementCondition: null
            type: craft\fieldlayoutelements\Markdown
            uid: 2c2b849a-acac-4d21-bc48-eb52af998c16
            userCondition: null
            width: 100
        name: Content
        uid: dfee2a76-d0cb-4ace-96d2-0f2259517f25
        userCondition: null
handle: testimonialSlider
hasTitleField: false
icon: shoe-prints
name: 'Testimonial Slider'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Testimonial Slider{ itemTitle ? '': '' ~ itemTitle }'
titleTranslationKeyFormat: null
titleTranslationMethod: site
