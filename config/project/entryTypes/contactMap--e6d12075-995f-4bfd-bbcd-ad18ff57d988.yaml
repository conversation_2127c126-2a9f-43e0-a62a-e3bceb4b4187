color: green
fieldLayouts:
  64c952ab-37b7-46e1-809f-f56f740ac075:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-14T23:54:29+00:00'
            elementCondition: null
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: dc7598d8-3be9-4633-ab12-04cb623e5931
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-14T23:30:44+00:00'
            elementCondition: null
            fieldUid: 58a0197d-774d-40de-b9a3-3e9dceccd8c1 # Richtext — Basic + Links
            handle: richtextBasicLinks
            includeInCards: false
            instructions: null
            label: Introduction
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3864a905-34a6-482d-9bed-314fddf3a5cf
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-14T23:30:44+00:00'
            elementCondition: null
            fieldUid: 5d3725da-5409-4f3e-bef1-d987f8529903 # Form
            handle: null
            includeInCards: false
            instructions: 'If you want a form other than the contact form, you can specify it here.'
            label: 'Override Form'
            providesThumbs: false
            required: false
            tip: ' If left empty, the contact form will be used.'
            type: craft\fieldlayoutelements\CustomField
            uid: 69fe6cc0-b31a-4277-951f-ec25536469b0
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: f2484666-a231-4190-b4c0-025864312eeb
        userCondition: null
handle: contactMap
hasTitleField: false
icon: shoe-prints
name: 'Contact & Map'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Contact & Map: { itemTitle }'
titleTranslationKeyFormat: null
titleTranslationMethod: site
