color: cyan
fieldLayouts:
  9a5a11ff-0e40-49e8-925e-d56ce0be40d7:
    tabs:
      -
        elementCondition: null
        name: Content
        uid: 84eba86e-3c55-49ae-8d43-79112dfd2dad
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-10T04:04:43+00:00'
            elementCondition: null
            fieldUid: 4cb26117-09e7-45e0-b133-7fb01223c980 # Section Header
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: cc5e565e-03c5-4fdf-b99b-f8a56e99ed2b
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-10T04:04:43+00:00'
            elementCondition: null
            fieldUid: ec3238b0-5e12-4a47-8f6f-7d65e1de859f # Section Footer
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7484c894-b33a-4269-ace7-************
            userCondition: null
            warning: null
            width: 100
        name: 'Header + Footer'
        uid: 30dde4ce-01d1-4a20-93e8-c9638f2cdc29
        userCondition: null
handle: localAttractions
hasTitleField: false
icon: plus
name: 'Local Attractions'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Local Attractions'
titleTranslationKeyFormat: null
titleTranslationMethod: site
