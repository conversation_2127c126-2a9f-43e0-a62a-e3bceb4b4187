color: blue
fieldLayouts:
  7c3dab29-7bcb-4352-a298-dd3d6aa571c9:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-09-30T07:47:34+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: bbcddc68-6a29-43e4-b3fd-06e450fea6b5
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-30T07:47:34+00:00'
            elementCondition: null
            fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
            handle: null
            includeInCards: true
            instructions: null
            label: 'Target Page'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0161aa71-6a77-42e9-9bd2-59b2ba7e72eb
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: fd89bb92-bce6-4c4e-be86-830c94d49644
        userCondition: null
handle: pageRedirect
hasTitleField: false
icon: arrow-trend-up
name: 'Page Redirect'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: ''
titleTranslationKeyFormat: null
titleTranslationMethod: site
