color: null
fieldLayouts:
  1da57712-8470-4412-be94-b94e0e6a789e:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 3bbeb07e-d59f-4566-8084-08f6a5f248ae
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: bce133b8-d726-4c05-a7c2-9353d45e3b86 # Richtext — Standard
            handle: copy
            includeInCards: false
            instructions: null
            label: Copy
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1dba0938-4e02-4eff-bacb-2596bcf63723
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 7ef99334-05ac-4c46-9b12-d7f27e9e57a0
        userCondition: null
handle: richtextBlock
hasTitleField: false
icon: cube
name: Richtext
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: ''
titleTranslationKeyFormat: null
titleTranslationMethod: site
