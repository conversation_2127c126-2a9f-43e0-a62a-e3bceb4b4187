color: cyan
fieldLayouts:
  f75e6734-8fea-4048-a243-db6bc5e27ae0:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-04-23T02:45:17+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\elements\conditions\entries\SectionConditionRule
                  operator: in
                  uid: 18a4c2a8-8771-41b4-a95b-9fc105c28d8b
                  values:
                    - a9fa4bce-4bdd-4816-bd77-97a96602fd81 # Home
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 465145dd-3bd1-4710-927e-28ed6b4aca6e # Background Colour
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 707ae028-7222-42ac-b4d0-61193e83a972
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-13T02:04:20+00:00'
            elementCondition: null
            fieldUid: 4cb26117-09e7-45e0-b133-7fb01223c980 # Section Header
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5c9ebaa0-006b-4d04-9ee5-8c641788e121
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-13T02:04:20+00:00'
            elementCondition: null
            fieldUid: ec3238b0-5e12-4a47-8f6f-7d65e1de859f # Section Footer
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0519d1dd-1a66-4420-8063-546938a8edf4
            userCondition: null
            warning: null
            width: 100
        name: 'Header + Footer'
        uid: 94068ebd-cc16-4aa6-9b55-cd815bcc7446
        userCondition: null
handle: accommodationGroups
hasTitleField: false
icon: plus
name: 'Accommodation Groups'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Accommodation Groups{{ sectionHeader.one() ?? false ? '': '' ~ sectionHeader.one().itemTitle }}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
