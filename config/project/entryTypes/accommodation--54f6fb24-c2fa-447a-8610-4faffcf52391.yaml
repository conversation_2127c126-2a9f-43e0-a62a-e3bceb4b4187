color: blue
fieldLayouts:
  f4fcd334-e37b-409e-9ddc-95d62482dba1:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            dismissible: false
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\EmptyFieldConditionRule
                  fieldUid: fc2a3f5e-eb9b-49a9-ba03-6fa3e62dd843 # Redirect
                  operator: notempty
                  uid: 53a497fc-a3bf-453d-9c93-5048273a6429
              elementType: craft\elements\Entry
              fieldContext: global
            style: warning
            tip: "This page is currently a redirect. \r\nYou can edit or remove the redirect under the *Settings* tab."
            type: craft\fieldlayoutelements\Tip
            uid: 701083c4-6fdd-4ea5-8456-3d855ba9c293
            userCondition: null
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-08-09T01:30:58+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: Title
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 07adcc44-7969-49ee-906f-958394e9796a
            userCondition: null
            warning: null
            width: 75
          -
            dateAdded: '2025-03-31T03:09:00+00:00'
            elementCondition: null
            fieldUid: a8f37862-5ca1-4285-b450-d208b321737e # IBE Category
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b89d5e82-de38-44a1-85eb-8834c0799890
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-29T05:35:17+00:00'
            elementCondition: null
            fieldUid: 679134c7-277f-4780-89d9-74f0e44d7938 # Target Url
            handle: bookingUrl
            includeInCards: false
            instructions: null
            label: 'Booking Url'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b9326605-bfd6-4e8a-ba7d-c84a3e8281ea
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-09T00:08:39+00:00'
            elementCondition: null
            fieldUid: 79160e23-8a39-493c-bf8c-85d7c2046f56 # Accommodation Type
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 56c4b2c7-c606-4209-be68-e7a63f22d087
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-24T03:06:57+00:00'
            elementCondition: null
            fieldUid: bd52763d-9d3d-4f4b-bdab-d9cc82292f83 # Page or Popup
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6ffbc834-c161-4732-b8f2-faa58feb6015
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-22T01:30:05+00:00'
            elementCondition: null
            fieldUid: 8b80e4e5-805f-4925-be62-29bccf682adc # Set Accommodation Group
            handle: null
            includeInCards: false
            instructions: null
            label: 'Accommodation Group'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a1b2b62e-a9ee-4330-a79f-38b8eda4bb09
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-09T00:08:39+00:00'
            elementCondition: null
            fieldUid: 692981e9-ded2-4931-bee6-40070fe65c66 # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 26a499c4-fd91-4c00-9173-8402636f299f
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-09T00:08:39+00:00'
            elementCondition: null
            fieldUid: 1f271e29-cf3d-46a5-9ad9-c04ccf0fd94a # Image
            handle: null
            includeInCards: false
            instructions: null
            label: 'Gallery Image'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 263d9908-d349-4005-977e-efdb006e9b15
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-09T00:29:26+00:00'
            elementCondition: null
            fieldUid: 89303a1b-8fec-4e93-9b39-9b5d6f2d4f22 # Excerpt
            handle: null
            includeInCards: false
            instructions: null
            label: 'Gallery Item Copy'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5ec6d4d9-2ba3-48a9-8285-7022d62928f0
            userCondition: null
            warning: null
            width: 75
          -
            dateAdded: '2025-01-09T00:08:39+00:00'
            elementCondition: null
            fieldUid: 5f0a2456-7f85-48d1-8c57-2de861d87916 # Guests
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6744d90f-bf0c-4e60-8450-d267eb5e5ac3
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-09T00:08:39+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: 79160e23-8a39-493c-bf8c-85d7c2046f56 # Accommodation Type
                  layoutElementUid: 56c4b2c7-c606-4209-be68-e7a63f22d087
                  uid: 745b77da-964d-41eb-861c-76e8b357d305
                  value: true
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 76beb404-d239-4da2-895a-058f0fd58c6e # Bedroom
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 16f0b001-5ad7-4fda-89bb-1c006de1216b
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-09T00:08:39+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: 79160e23-8a39-493c-bf8c-85d7c2046f56 # Accommodation Type
                  layoutElementUid: 56c4b2c7-c606-4209-be68-e7a63f22d087
                  uid: 33dc4f55-9d2f-4058-b913-2212feba7725
                  value: true
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 566c2e21-8303-4fae-8333-ace60cbb280a # Bathroom
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 57272378-d8b3-448c-b75b-ccafa9b5a003
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-09T00:08:39+00:00'
            elementCondition: null
            fieldUid: e35e3d12-5c96-4c46-89a3-503288c4e477 # Pet Friendly
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9d52be10-158e-4248-9cb6-b126c32b2fd8
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-09T00:08:39+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: 79160e23-8a39-493c-bf8c-85d7c2046f56 # Accommodation Type
                  layoutElementUid: 56c4b2c7-c606-4209-be68-e7a63f22d087
                  uid: 96c95d56-cde1-4dcc-b106-0f9e097beb36
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: ba78e5dc-9e4e-4d57-986b-0e5b00fdf3df # Powered
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 559410ba-7b2e-48c7-a191-83f1497fd080
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-13T06:35:19+00:00'
            elementCondition: null
            fieldUid: bb32921b-628e-473f-9893-720bebbcefa2 # Accommodation Tags
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: af16e3d4-4604-4a82-9064-7e7debe4488f
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-09T00:08:39+00:00'
            elementCondition: null
            fieldUid: 27aab6f1-8595-41c8-8e89-0a997f2cca61 # Video
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7d761122-aec3-46f0-9001-d7003f5773b1
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-09T00:08:39+00:00'
            elementCondition: null
            fieldUid: 2f4c6551-4431-4352-8c81-6989358d2bbc # Image List
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 220d3097-f40a-4998-b91b-881705d0d9cf
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-04-04T03:53:42+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 2f4c6551-4431-4352-8c81-6989358d2bbc # Image List
                  layoutElementUid: 220d3097-f40a-4998-b91b-881705d0d9cf
                  operator: empty
                  uid: 1e1d11f0-4502-4f99-b842-408e60e5cb7f
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: d6c9f943-51b9-4310-a0b8-967c3197574d # Accommodation Images
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 540702c1-00ee-4d86-917b-b2180446dba4
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-09T00:24:09+00:00'
            elementCondition: null
            fieldUid: 3eba5399-078e-49f7-ba3f-a93b1d553b13 # Features
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0b9de872-e810-40da-8f24-0d0f54e5cdeb
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-09T00:32:34+00:00'
            elementCondition: null
            fieldUid: 719fbe54-999b-4265-9301-c64e2f05bd1e # Introduction
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 83add129-8c57-4648-97c5-0f3b103bd8bc
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-12T02:39:08+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\EmptyFieldConditionRule
                  fieldUid: 719fbe54-999b-4265-9301-c64e2f05bd1e # Introduction
                  layoutElementUid: 83add129-8c57-4648-97c5-0f3b103bd8bc
                  operator: empty
                  uid: 3398d041-c252-435d-b483-c0cb0ab6cf29
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 692981e9-ded2-4931-bee6-40070fe65c66 # Subtitle
            handle: accommodationHeading
            includeInCards: false
            instructions: null
            label: 'Accommodation Heading'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9442f012-2015-4952-bb63-7cb6f8146355
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-12T02:19:41+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\EmptyFieldConditionRule
                  fieldUid: 719fbe54-999b-4265-9301-c64e2f05bd1e # Introduction
                  layoutElementUid: 83add129-8c57-4648-97c5-0f3b103bd8bc
                  operator: empty
                  uid: 2ec1d29b-9ea1-4ee4-802e-fee1d5c5bb64
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: f2ce869f-c822-4794-a9a4-3c323653dd42 # Accommodation Description
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 26f8d8bf-c4e3-456e-9d68-f8b83fdc2d93
            userCondition: null
            warning: null
            width: 100
        name: Pages
        uid: 54d8c8b6-8606-474a-8303-8619ae7f71e2
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: 4ed4074e-eb71-49d9-b9a9-05f308dcd29a # Navigation Title
            handle: null
            includeInCards: false
            instructions: null
            label: 'Navigation Title'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: *************-4234-b220-a353549447f4
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: 2f4881f2-619d-484d-8bae-035941bc9724 # Navigation Visibility
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 34ff1118-62ea-4383-9ff0-5615c8b7423b
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            heading: Meta
            type: craft\fieldlayoutelements\Heading
            uid: 9c254bbb-6a7c-4d35-8bec-c441e3df58dd
            userCondition: null
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: afb06c07-eee2-4c2f-a623-7c5976c82755 # Meta Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a569a91d-5976-43cd-9dea-cd83d26d5035
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: f85a92fb-60aa-4278-84f7-0e9c0d7765f2 # Meta Description
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 90fa3702-9d05-4891-b95a-35dee216145c
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: 771d90ed-3e58-4e4b-8340-54e0af52cc39 # Meta Keywords
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3ba4f3ca-58a1-4b15-a12c-ec75e4f2f7ab
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-14T02:34:07+00:00'
            elementCondition: null
            fieldUid: 228d33d7-3ea6-427d-947d-02bf8fa66bfd # Meta Share Image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3c26c42b-205b-4ebb-9b4e-e0a9632e0d4a
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:32:22+00:00'
            elementCondition: null
            fieldUid: 6c842807-a0bf-4772-9fac-7ac0332c105f # Canonical Url
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ee07b5be-0c9c-47f3-949a-83636879b84a
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            heading: Redirect
            type: craft\fieldlayoutelements\Heading
            uid: 54e1196a-9b48-40c3-89d2-88db013463d2
            userCondition: null
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: fc2a3f5e-eb9b-49a9-ba03-6fa3e62dd843 # Redirect
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7b264b91-02bc-43a6-a9b7-4377a325e06a
            userCondition: null
            warning: null
            width: 100
        name: Settings
        uid: 5d71dfdd-5f5f-4888-ae69-39c59fad5c42
        userCondition: null
handle: accommodation
hasTitleField: true
icon: bed
name: Accommodation
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
