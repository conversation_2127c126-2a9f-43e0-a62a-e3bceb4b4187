color: blue
fieldLayouts:
  c4206291-da82-4b76-ad4a-d6d7b2162780:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            dismissible: false
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\EmptyFieldConditionRule
                  fieldUid: fc2a3f5e-eb9b-49a9-ba03-6fa3e62dd843 # Redirect
                  operator: notempty
                  uid: 53a497fc-a3bf-453d-9c93-5048273a6429
              elementType: craft\elements\Entry
              fieldContext: global
            style: warning
            tip: "This page is currently a redirect. \r\nYou can edit or remove the redirect under the *Settings* tab."
            type: craft\fieldlayoutelements\Tip
            uid: 701083c4-6fdd-4ea5-8456-3d855ba9c293
            userCondition: null
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-08-09T01:30:58+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: Title
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 07adcc44-7969-49ee-906f-958394e9796a
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-02-10T23:45:08+00:00'
            elementCondition: null
            fieldUid: ab02ade8-b3bc-4493-a157-2da8bc230c1c # Use Global Book Link
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2c639a92-a5e6-41af-8837-b0fbfb372432
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-21T04:42:59+00:00'
            elementCondition: null
            fieldUid: 692981e9-ded2-4931-bee6-40070fe65c66 # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8fc93b88-afc4-4e65-87f6-9ca52d5eed14
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-09T04:58:00+00:00'
            elementCondition: null
            fieldUid: 692981e9-ded2-4931-bee6-40070fe65c66 # Subtitle
            handle: tagline
            includeInCards: false
            instructions: null
            label: Tagline
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2e1865e5-6512-4396-9ab2-8ca9d8fbaf92
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-09T04:56:21+00:00'
            elementCondition: null
            fieldUid: 58a0197d-774d-40de-b9a3-3e9dceccd8c1 # Richtext — Basic + Links
            handle: introduction
            includeInCards: false
            instructions: null
            label: Introduction
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8b84afd9-2400-4d4e-a599-f486848cd966
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: 04c9645d-ce63-4f71-930f-f0438bd168f3 # Banner
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: fec5d22c-7021-4508-bf55-54719562070c
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-12-27T04:41:00+00:00'
            elementCondition: null
            fieldUid: 1f271e29-cf3d-46a5-9ad9-c04ccf0fd94a # Image
            handle: null
            includeInCards: false
            instructions: null
            label: 'Gallery Thumbnail'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 87153e9c-0a51-4e1d-b95b-7bb3be785687
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-13T04:21:10+00:00'
            elementCondition: null
            fieldUid: 2f4c6551-4431-4352-8c81-6989358d2bbc # Image List
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4e609a1f-1b91-4bab-9957-b96b76a02698
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-13T02:55:52+00:00'
            elementCondition: null
            fieldUid: 58a0197d-774d-40de-b9a3-3e9dceccd8c1 # Richtext — Basic + Links
            handle: null
            includeInCards: false
            instructions: null
            label: 'Card Copy'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0a53eaaa-a748-49e8-a9c5-af90608e6900
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: 0adc5399-0e40-4d46-9443-a85046d2751d # Page Content
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b68ac48d-a0d5-445e-a0e6-fc6b615b0f1c
            userCondition: null
            warning: null
            width: 100
        name: Pages
        uid: 54d8c8b6-8606-474a-8303-8619ae7f71e2
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-02-13T23:11:00+00:00'
            elementCondition: null
            fieldUid: da66d2a6-d308-4a4b-9afa-79e244997c0a # Use Custom Prefooter
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 25d966e6-ce54-4e88-bcde-293787cd1077
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-02-13T23:11:00+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: da66d2a6-d308-4a4b-9afa-79e244997c0a # Use Custom Prefooter
                  layoutElementUid: 25d966e6-ce54-4e88-bcde-293787cd1077
                  uid: a8cce1fe-d51f-4515-817b-61c59c41c36d
                  value: true
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 7e433a91-21a2-4de6-93bc-5278e47d6fc6 # Custom Prefooter
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e40d8219-3653-457e-961a-0ff994e0342b
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-02-13T23:11:00+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: 25c249e6-a308-49e4-aef3-cc125683ec65
            userCondition: null
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: ed08dded-d02c-4a15-a218-b5e7b8370a35 # Enable Sidebar
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 415a642b-e844-487b-8e4c-c6ad1fce32f3
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: ed08dded-d02c-4a15-a218-b5e7b8370a35 # Enable Sidebar
                  uid: 4d3d1c6f-8aaa-4ace-9022-49cf738c49a8
                  value: true
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 98c2e58b-0203-4aa4-89af-4036d893dfca # Sidebar Addons
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 92ed7c42-e2ff-4848-b193-1388bf8c7d52
            userCondition: null
            warning: null
            width: 50
        name: Layout
        uid: 1b86c511-bee6-43c9-a664-8890e32229cf
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: 4ed4074e-eb71-49d9-b9a9-05f308dcd29a # Navigation Title
            handle: null
            includeInCards: false
            instructions: null
            label: 'Navigation Title'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: *************-4234-b220-a353549447f4
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: 2f4881f2-619d-484d-8bae-035941bc9724 # Navigation Visibility
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 34ff1118-62ea-4383-9ff0-5615c8b7423b
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            heading: Meta
            type: craft\fieldlayoutelements\Heading
            uid: 9c254bbb-6a7c-4d35-8bec-c441e3df58dd
            userCondition: null
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: afb06c07-eee2-4c2f-a623-7c5976c82755 # Meta Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a569a91d-5976-43cd-9dea-cd83d26d5035
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: f85a92fb-60aa-4278-84f7-0e9c0d7765f2 # Meta Description
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 90fa3702-9d05-4891-b95a-35dee216145c
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: 771d90ed-3e58-4e4b-8340-54e0af52cc39 # Meta Keywords
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3ba4f3ca-58a1-4b15-a12c-ec75e4f2f7ab
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-14T02:34:07+00:00'
            elementCondition: null
            fieldUid: 228d33d7-3ea6-427d-947d-02bf8fa66bfd # Meta Share Image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3c26c42b-205b-4ebb-9b4e-e0a9632e0d4a
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:32:22+00:00'
            elementCondition: null
            fieldUid: 6c842807-a0bf-4772-9fac-7ac0332c105f # Canonical Url
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ee07b5be-0c9c-47f3-949a-83636879b84a
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            heading: Redirect
            type: craft\fieldlayoutelements\Heading
            uid: 54e1196a-9b48-40c3-89d2-88db013463d2
            userCondition: null
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: fc2a3f5e-eb9b-49a9-ba03-6fa3e62dd843 # Redirect
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7b264b91-02bc-43a6-a9b7-4377a325e06a
            userCondition: null
            warning: null
            width: 100
        name: Settings
        uid: 5d71dfdd-5f5f-4888-ae69-39c59fad5c42
        userCondition: null
handle: accommodationGroup
hasTitleField: true
icon: van-shuttle
name: 'Accommodation Group'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
