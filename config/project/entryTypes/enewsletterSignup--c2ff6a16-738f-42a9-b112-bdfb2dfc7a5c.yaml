color: green
fieldLayouts:
  d7a6642b-5f49-452e-b6c0-d748c576e16f:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-14T23:35:30+00:00'
            elementCondition: null
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a4aa5ea8-dfff-4cae-b38a-89ac6b94c1cb
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-14T23:35:30+00:00'
            elementCondition: null
            fieldUid: 58a0197d-774d-40de-b9a3-3e9dceccd8c1 # Richtext — Basic + Links
            handle: null
            includeInCards: false
            instructions: null
            label: Copy
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 47de08ea-09ab-474a-9f5a-662bcc29e1dd
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-14T23:35:30+00:00'
            elementCondition: null
            fieldUid: 5d3725da-5409-4f3e-bef1-d987f8529903 # Form
            handle: null
            includeInCards: false
            instructions: 'If you want a form other than the enews signup form, you can specify it here.'
            label: 'Override Form'
            providesThumbs: false
            required: false
            tip: ' If left empty, the enews signup form will be used.'
            type: craft\fieldlayoutelements\CustomField
            uid: 756cbaf1-7af5-40c2-89a6-5c04522c7852
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: db49b21e-b733-4423-b7bd-37025b622de6
        userCondition: null
handle: enewsletterSignup
hasTitleField: false
icon: shoe-prints
name: 'eNewsletter Signup'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Enews Signup: { itemTitle }'
titleTranslationKeyFormat: null
titleTranslationMethod: site
