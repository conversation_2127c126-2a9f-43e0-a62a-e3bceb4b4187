color: blue
fieldLayouts:
  b4c5c961-aa33-45a8-a5b1-5ff015e54229:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-10T04:12:19+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\TextFieldConditionRule
                  fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
                  layoutElementUid: 52260890-735d-4a9e-972a-3fae9d72f76f
                  operator: empty
                  uid: 78ab48f8-92f8-41aa-aac0-205e97fb8908
                  value: ''
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 1f271e29-cf3d-46a5-9ad9-c04ccf0fd94a # Image
                  layoutElementUid: 5272b48b-11f9-42a6-816a-f6561e836f46
                  operator: empty
                  uid: dbff3158-4a58-4b1c-8b2f-95b4d6eff1f4
                -
                  class: craft\fields\conditions\EmptyFieldConditionRule
                  fieldUid: 27aab6f1-8595-41c8-8e89-0a997f2cca61 # Video
                  layoutElementUid: 14682110-0df3-4fdb-9c4e-26b32755161e
                  operator: empty
                  uid: 096baccd-196f-4505-a97b-32fea383e2b8
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 7d9a07fe-084f-4520-84c8-f73991962aac # Icon
                  layoutElementUid: a9c21158-0463-44e4-af25-021f50732544
                  operator: empty
                  uid: 1a0dab71-9657-4637-bad4-d69e442e522e
                -
                  class: craft\fields\conditions\TextFieldConditionRule
                  fieldUid: d6f4d67f-350d-4c39-a6d7-56a5f5f001af # Link
                  layoutElementUid: 30983f7a-e106-4f7d-8826-b27997e82fa7
                  operator: empty
                  uid: 13b2ca30-c8b6-40a0-9c99-c47d7adc0650
                  value: ''
                -
                  class: craft\fields\conditions\EmptyFieldConditionRule
                  fieldUid: 58a0197d-774d-40de-b9a3-3e9dceccd8c1 # Richtext — Basic + Links
                  layoutElementUid: 377e9fb1-f51c-44dc-bcd4-0421e0a5976a
                  operator: empty
                  uid: aa80686a-51dd-44cf-b02b-caf4d6743340
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
            handle: specificEntry
            includeInCards: false
            instructions: 'Optional: automatically populate the card content using a specific entry.'
            label: 'Specific Entry'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 78152ba0-324f-4a74-8086-b4a5ac92ed29
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-10T05:33:48+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: d78ca3d8-8021-45b1-8cdc-052ec411db30
            userCondition: null
          -
            dateAdded: '2024-08-07T02:55:19+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 7d9a07fe-084f-4520-84c8-f73991962aac # Icon
                  layoutElementUid: a9c21158-0463-44e4-af25-021f50732544
                  operator: empty
                  uid: 8aa63f8c-121d-44e2-a252-59d9c916e29b
                -
                  class: craft\fields\conditions\EmptyFieldConditionRule
                  fieldUid: 27aab6f1-8595-41c8-8e89-0a997f2cca61 # Video
                  layoutElementUid: 14682110-0df3-4fdb-9c4e-26b32755161e
                  operator: empty
                  uid: 4ebab0d5-b623-41c3-ace8-fd69ecc8d439
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
                  layoutElementUid: 78152ba0-324f-4a74-8086-b4a5ac92ed29
                  operator: empty
                  uid: 0683d249-a5f5-41df-a65e-109b53fd8916
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 1f271e29-cf3d-46a5-9ad9-c04ccf0fd94a # Image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5272b48b-11f9-42a6-816a-f6561e836f46
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-12-19T00:23:03+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 1f271e29-cf3d-46a5-9ad9-c04ccf0fd94a # Image
                  layoutElementUid: 5272b48b-11f9-42a6-816a-f6561e836f46
                  operator: empty
                  uid: b35f05a2-f616-4740-8009-33ede00340b8
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 7d9a07fe-084f-4520-84c8-f73991962aac # Icon
                  layoutElementUid: a9c21158-0463-44e4-af25-021f50732544
                  operator: empty
                  uid: a2d0c510-aefe-469e-9d05-64fddc16d0ad
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
                  layoutElementUid: 78152ba0-324f-4a74-8086-b4a5ac92ed29
                  operator: empty
                  uid: cf896c81-d23e-44a1-bd1f-c8f4fa83a96e
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 27aab6f1-8595-41c8-8e89-0a997f2cca61 # Video
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 14682110-0df3-4fdb-9c4e-26b32755161e
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-12-19T00:23:03+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 1f271e29-cf3d-46a5-9ad9-c04ccf0fd94a # Image
                  layoutElementUid: 5272b48b-11f9-42a6-816a-f6561e836f46
                  operator: empty
                  uid: dedc6b5a-4a2c-4074-8f29-fc75f3d7847a
                -
                  class: craft\fields\conditions\EmptyFieldConditionRule
                  fieldUid: 27aab6f1-8595-41c8-8e89-0a997f2cca61 # Video
                  layoutElementUid: 14682110-0df3-4fdb-9c4e-26b32755161e
                  operator: empty
                  uid: b7bfefa8-c80a-4b4d-a501-1ccd0fe8be9a
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
                  layoutElementUid: 78152ba0-324f-4a74-8086-b4a5ac92ed29
                  operator: empty
                  uid: 8646e4c8-254e-450d-8f94-16da23242b68
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 7d9a07fe-084f-4520-84c8-f73991962aac # Icon
            handle: iconAsset
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: 'For best results use a `.svg` file'
            type: craft\fieldlayoutelements\CustomField
            uid: a9c21158-0463-44e4-af25-021f50732544
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-12-19T00:24:57+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: b750a2fd-7bbe-4214-bb69-27ba3797454f
            userCondition: null
          -
            dateAdded: '2024-08-07T02:55:19+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
                  layoutElementUid: 78152ba0-324f-4a74-8086-b4a5ac92ed29
                  operator: empty
                  uid: f88f1f17-d66e-4371-9eaf-54a405263821
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 52260890-735d-4a9e-972a-3fae9d72f76f
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-07T02:55:19+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
                  layoutElementUid: 78152ba0-324f-4a74-8086-b4a5ac92ed29
                  operator: empty
                  uid: b27b7c82-db7b-469a-8ec5-50f61fbaafde
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 58a0197d-774d-40de-b9a3-3e9dceccd8c1 # Richtext — Basic + Links
            handle: null
            includeInCards: false
            instructions: null
            label: 'Short Description'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 377e9fb1-f51c-44dc-bcd4-0421e0a5976a
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-18T22:43:36+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
                  layoutElementUid: 78152ba0-324f-4a74-8086-b4a5ac92ed29
                  operator: empty
                  uid: 7d7eaa36-a693-4fe7-bd13-73e405637905
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: d6f4d67f-350d-4c39-a6d7-56a5f5f001af # Link
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 30983f7a-e106-4f7d-8826-b27997e82fa7
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 856edd8b-b3f3-4715-b7fb-b4763e9dd7ef
        userCondition: null
handle: card
hasTitleField: false
icon: card-diamond
name: Card
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
