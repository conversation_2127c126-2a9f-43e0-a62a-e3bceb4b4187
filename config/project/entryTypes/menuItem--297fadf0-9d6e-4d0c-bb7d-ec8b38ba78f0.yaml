color: blue
fieldLayouts:
  73a98b6c-652a-4a89-b510-bdc858588c3f:
    cardView:
      - 'layoutElement:5fe381c8-a74f-4459-a5ea-cf7cc3639e6e'
      - 'layoutElement:14c79ead-4c85-4f3e-8a8b-f5c7781316e6'
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-07T00:02:02+00:00'
            elementCondition: null
            fieldUid: d6f4d67f-350d-4c39-a6d7-56a5f5f001af # Link
            handle: null
            includeInCards: true
            instructions: null
            label: Link
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5fe381c8-a74f-4459-a5ea-cf7cc3639e6e
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T00:02:02+00:00'
            elementCondition: null
            fieldUid: 692981e9-ded2-4931-bee6-40070fe65c66 # Subtitle
            handle: null
            includeInCards: true
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 14c79ead-4c85-4f3e-8a8b-f5c7781316e6
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T00:33:41+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: e1c72882-d931-4855-8a46-cddb250c5a83
            userCondition: null
          -
            dateAdded: '2025-01-07T00:08:07+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\EmptyFieldConditionRule
                  fieldUid: 5db558bc-3b1b-490f-bed2-5580e0996c12 # Navigation Link Helper
                  layoutElementUid: 2a62aa42-6663-40af-9c13-b7c044a36fb5
                  operator: empty
                  uid: 68b99940-5d5a-44e3-8c3d-fcb8de3968cf
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 5c3e639e-7e54-49f8-9ec8-dc0e8cb04bdb # Show All Children Pages
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 51cce972-687e-448f-affa-bf2d1a3fa6fe
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T00:18:28+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: 5c3e639e-7e54-49f8-9ec8-dc0e8cb04bdb # Show All Children Pages
                  layoutElementUid: 51cce972-687e-448f-affa-bf2d1a3fa6fe
                  uid: 073ba5b6-c7d8-4364-8284-963fb7407d0f
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 5db558bc-3b1b-490f-bed2-5580e0996c12 # Navigation Link Helper
            handle: customSecondaryLinks
            includeInCards: false
            instructions: null
            label: 'Custom Secondary Links'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2a62aa42-6663-40af-9c13-b7c044a36fb5
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 691a5e9e-0026-4e95-9bdc-ce3128e2f774
        userCondition: null
handle: menuItem
hasTitleField: false
icon: bars
name: 'Menu Item'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Menu Item: { linkField.label }'
titleTranslationKeyFormat: null
titleTranslationMethod: site
