color: blue
fieldLayouts:
  f1514d55-645f-4725-b5ff-7d2751f7e0bc:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-08T00:30:42+00:00'
            elementCondition: null
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2c8e8cec-**************-bde6ed9f2282
            userCondition: null
            warning: null
            width: 75
          -
            dateAdded: '2025-04-23T02:37:10+00:00'
            elementCondition: null
            fieldUid: 465145dd-3bd1-4710-927e-28ed6b4aca6e # Background Colour
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2667d356-04d9-410b-a936-13f04e0ebc31
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-08T00:30:42+00:00'
            elementCondition: null
            fieldUid: 58a0197d-774d-40de-b9a3-3e9dceccd8c1 # Richtext — Basic + Links
            handle: columnOne
            includeInCards: false
            instructions: null
            label: 'Column One'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: *************-4dc5-a3f5-3ffca196abfc
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-08T01:29:44+00:00'
            elementCondition: null
            fieldUid: 58a0197d-774d-40de-b9a3-3e9dceccd8c1 # Richtext — Basic + Links
            handle: columnTwo
            includeInCards: false
            instructions: null
            label: 'Column Two'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e5099db0-b86c-4114-b761-720f01cd6491
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-08T00:30:42+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\elements\conditions\entries\FieldConditionRule
                  operator: ni
                  uid: c3922006-b1b6-463f-b547-f8c03144a398
                  values:
                    - 719fbe54-999b-4265-9301-c64e2f05bd1e # Introduction
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: ca688c53-4fa3-4026-a0bb-4d86bb5ac683 # Link Helper
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9cd1a4cd-1f5b-4e7b-8bb8-67c663863ef7
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: fb4b3f6a-14f5-463c-9b64-7ef5f4b90f19
        userCondition: null
handle: introduction
hasTitleField: false
icon: circle
name: Introduction
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Introduction: { itemTitle }'
titleTranslationKeyFormat: null
titleTranslationMethod: site
