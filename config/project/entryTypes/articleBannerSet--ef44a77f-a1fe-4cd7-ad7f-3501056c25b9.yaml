color: cyan
fieldLayouts:
  6a2352ba-9fa1-4635-af24-58ea9f510505:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-03T01:46:23+00:00'
            elementCondition: null
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: 'Admin Title'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5031b219-ba8f-47f4-bb10-************
            userCondition: null
            warning: null
            width: 75
          -
            dateAdded: '2025-01-14T04:14:08+00:00'
            elementCondition: null
            fieldUid: 20faef3f-1567-45d6-b0a6-abbd33597ff0 # Banner Text Position
            handle: contentPosition
            includeInCards: false
            instructions: null
            label: 'Content Position'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2f30e92f-bb7c-4b1f-b03a-7f1f99225da9
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-14T01:33:55+00:00'
            elementCondition: null
            fieldUid: 2d60ea84-1168-4081-b7b5-3a9166348eb7 # Article Helper
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9f28228e-f492-4111-9a2b-826a9108612d
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 1419e465-30a8-4fbd-8231-05e3866ee597
        userCondition: null
handle: articleBannerSet
hasTitleField: false
icon: plus
name: 'Article Banner Set'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Article Banner Set: { itemTitle }'
titleTranslationKeyFormat: null
titleTranslationMethod: none
