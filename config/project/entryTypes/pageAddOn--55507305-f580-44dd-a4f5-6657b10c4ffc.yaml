color: null
fieldLayouts:
  ccd948c8-bea5-4caf-9571-5eb69c60b179:
    cardView:
      - 'layoutElement:5e513b9f-8602-4bed-8817-af538ef72447'
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-27T03:06:55+00:00'
            elementCondition: null
            fieldUid: e6cf7a49-c3b8-48a3-b530-98a087429cda # Add-on Item
            handle: null
            includeInCards: true
            instructions: null
            label: 'Add-on Item'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5e513b9f-8602-4bed-8817-af538ef72447
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-12-27T03:06:55+00:00'
            elementCondition: null
            fieldUid: c8b583af-2579-4a45-b50c-a9d37005074a # Panel Width
            handle: null
            includeInCards: false
            instructions: null
            label: 'Panel Width'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 70e71ac0-efa4-4aaf-9898-5a97e7e693d5
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-12-27T03:08:14+00:00'
            elementCondition: null
            fieldUid: 465145dd-3bd1-4710-927e-28ed6b4aca6e # Background Colour
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c34a2f50-916c-40d6-ad73-ce9b602a8f49
            userCondition: null
            warning: null
            width: 25
        name: Content
        uid: 507a4899-2078-47d4-86f0-e826124ae47b
        userCondition: null
handle: pageAddOn
hasTitleField: false
icon: cube
name: 'Page Add-on'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
