color: cyan
fieldLayouts:
  43d7b196-4065-44f7-9341-4aa8c290dc51:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-27T01:34:12+00:00'
            elementCondition: null
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: 'Custom Admin Title'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0cdeda75-dfce-44c9-b776-9e7727bccd65
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-27T00:52:50+00:00'
            elementCondition: null
            fieldUid: bfb19cb5-8105-4c0d-92e0-70934b26e830 # Post Category
            handle: null
            includeInCards: false
            instructions: 'Leave empty to show all categories'
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 70a53c48-ec6a-42a3-b6de-806bc6e43ecb
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-12-27T00:52:50+00:00'
            elementCondition: null
            fieldUid: 0d420372-a13e-4813-99fc-5fddc267fb34 # Limit
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9b3b6d57-fbde-4fd7-890d-f403554d5226
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-12-27T00:52:50+00:00'
            elementCondition: null
            fieldUid: e6626276-093e-46d3-9e56-deb284e72876 # Show Hide
            handle: null
            includeInCards: false
            instructions: null
            label: 'Category Filters'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6a04651b-4e12-4650-b687-793a16faa1fd
            userCondition: null
            warning: null
            width: 25
        name: Content
        uid: 7c07df93-1f98-4839-b36d-bfb492c640eb
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-27T00:54:09+00:00'
            elementCondition: null
            fieldUid: 4cb26117-09e7-45e0-b133-7fb01223c980 # Section Header
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a36d23d4-5e25-4956-a2ef-ec8723696fa8
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-27T01:18:55+00:00'
            elementCondition: null
            fieldUid: ec3238b0-5e12-4a47-8f6f-7d65e1de859f # Section Footer
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 512b75e0-d33b-44e0-8ed2-57b9c44cee14
            userCondition: null
            warning: null
            width: 100
        name: 'Header + Footer'
        uid: a663065d-1770-4500-a66d-a71bbe0249ac
        userCondition: null
handle: postsGallery
hasTitleField: false
icon: plus
name: 'Posts Gallery'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Posts Gallery: { itemTitle ? itemTitle : (postCategory.one().title ?: ''All'') }'
titleTranslationKeyFormat: null
titleTranslationMethod: site
