color: blue
fieldLayouts:
  a2f99e2d-4cd9-402b-b926-5ef91945c3ab:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-14T01:40:07+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  elementId: null
                  fieldUid: 04c9645d-ce63-4f71-930f-f0438bd168f3 # Banner
                  layoutElementUid: d89466ad-0519-49bb-8844-403a0dc3c83f
                  operator: empty
                  uid: 6c92fded-c64a-4c13-ad45-1ffaa11113ee
                -
                  class: craft\fields\conditions\TextFieldConditionRule
                  fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
                  layoutElementUid: 766f13b9-f447-4249-b10c-cbcdbee3936b
                  operator: empty
                  uid: faffb563-5770-4e83-8c49-8c2c8bebf4a4
                  value: ''
                -
                  class: craft\fields\conditions\TextFieldConditionRule
                  fieldUid: d6f4d67f-350d-4c39-a6d7-56a5f5f001af # Link
                  layoutElementUid: 315bc34a-7f18-4930-aef5-e9d5a910713c
                  operator: empty
                  uid: 3e5114fa-6704-4e78-b1f2-5d487aa06361
                  value: ''
                -
                  class: craft\fields\conditions\EmptyFieldConditionRule
                  fieldUid: 58a0197d-774d-40de-b9a3-3e9dceccd8c1 # Richtext — Basic + Links
                  layoutElementUid: 4f016533-6f95-4a45-8c04-5110618988dc
                  operator: empty
                  uid: 37bb574f-d66f-485e-b23c-8f3426a32943
                -
                  class: craft\fields\conditions\TextFieldConditionRule
                  fieldUid: 692981e9-ded2-4931-bee6-40070fe65c66 # Subtitle
                  layoutElementUid: dd8d06b7-cf32-4a45-9bdc-d9f6684c2329
                  operator: empty
                  uid: 2f1cea37-82de-4c4b-b67f-b306c0555c91
                  value: ''
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
            handle: specificEntry
            includeInCards: false
            instructions: 'Optional: automatically populate the article content using a specific entry.'
            label: 'Specific Entry'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 743db123-98ee-4828-a319-7c2234a7827b
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-14T01:40:07+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: a7027844-5012-4954-bb74-ead35ab8a564
            userCondition: null
          -
            dateAdded: '2025-01-14T01:40:07+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  elementId: null
                  fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
                  layoutElementUid: 743db123-98ee-4828-a319-7c2234a7827b
                  operator: empty
                  uid: 0ba283f8-875b-4232-858d-4c0b6d69558f
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 04c9645d-ce63-4f71-930f-f0438bd168f3 # Banner
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d89466ad-0519-49bb-8844-403a0dc3c83f
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-14T01:40:07+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  elementId: null
                  fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
                  layoutElementUid: 743db123-98ee-4828-a319-7c2234a7827b
                  operator: empty
                  uid: de740cd5-297a-4221-9c91-2056a1b560eb
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 766f13b9-f447-4249-b10c-cbcdbee3936b
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-14T01:40:07+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  elementId: null
                  fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
                  layoutElementUid: 743db123-98ee-4828-a319-7c2234a7827b
                  operator: empty
                  uid: 83ffd780-806d-467d-b876-b3567f5fb01d
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 692981e9-ded2-4931-bee6-40070fe65c66 # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: 'Type / Subtitle'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: dd8d06b7-cf32-4a45-9bdc-d9f6684c2329
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-14T01:40:07+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  elementId: null
                  fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
                  layoutElementUid: 743db123-98ee-4828-a319-7c2234a7827b
                  operator: empty
                  uid: dfa541dc-a714-42e8-8ae0-d36123f51f84
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 58a0197d-774d-40de-b9a3-3e9dceccd8c1 # Richtext — Basic + Links
            handle: null
            includeInCards: false
            instructions: null
            label: 'Short Description'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4f016533-6f95-4a45-8c04-5110618988dc
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-14T01:40:07+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\RelationalFieldConditionRule
                  elementId: null
                  fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
                  layoutElementUid: 743db123-98ee-4828-a319-7c2234a7827b
                  operator: empty
                  uid: e6d3d409-8608-4397-bdc2-44f472785668
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: d6f4d67f-350d-4c39-a6d7-56a5f5f001af # Link
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 315bc34a-7f18-4930-aef5-e9d5a910713c
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 267cd23b-da37-41d1-9c0a-239fad15a3d2
        userCondition: null
handle: article
hasTitleField: false
icon: memo
name: Article
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
