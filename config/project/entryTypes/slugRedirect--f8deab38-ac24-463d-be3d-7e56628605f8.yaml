color: blue
fieldLayouts:
  b4e763f2-6322-4735-8efd-f1d2c9df9ddc:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-09-30T07:47:57+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 881c1884-936f-4e36-8e68-817149dbb266
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-30T07:47:57+00:00'
            elementCondition: null
            fieldUid: 6849b023-bb8b-4592-882f-478dd9aa06d8 # Target Slug
            handle: null
            includeInCards: true
            instructions: null
            label: 'Target Slug'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1be3df74-ec7a-460c-98ae-f7b11c06f76c
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: d423ede8-9dd3-4f43-80d8-2ab280ec0a3b
        userCondition: null
handle: slugRedirect
hasTitleField: false
icon: arrow-trend-up
name: Slug
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: ''
titleTranslationKeyFormat: null
titleTranslationMethod: site
