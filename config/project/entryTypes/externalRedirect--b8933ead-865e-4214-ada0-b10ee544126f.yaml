color: blue
fieldLayouts:
  ff5ce9b1-49be-4ba1-9c3c-95bae08bf05b:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-09-30T07:47:48+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 906725fb-2ed7-40f3-b9c6-4954111dfe05
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-30T07:47:48+00:00'
            elementCondition: null
            fieldUid: 679134c7-277f-4780-89d9-74f0e44d7938 # Target Url
            handle: null
            includeInCards: true
            instructions: null
            label: 'Target Url'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ba40f8ed-63fc-4cb3-8a00-8728c1f83baf
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 0a8ce80b-f039-4be3-be0a-70df92cf866c
        userCondition: null
handle: externalRedirect
hasTitleField: false
icon: arrow-trend-up
name: External
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: ''
titleTranslationKeyFormat: null
titleTranslationMethod: site
