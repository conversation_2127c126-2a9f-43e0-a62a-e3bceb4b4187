color: blue
fieldLayouts:
  1daf0f8e-8786-4c07-a1a3-a524d546e392:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-07T00:04:00+00:00'
            elementCondition: null
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6ce27202-c693-4a8a-8e18-a912deb710d2
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-07T00:04:00+00:00'
            elementCondition: null
            fieldUid: 75436df8-efa6-4034-85a6-6fa92ec92df5 # Menu Items
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9af1846f-65b5-4288-98c0-27c9f9792e2a
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 4ba7924a-4879-4951-b3ed-e63c6ae446b8
        userCondition: null
handle: menuColumn
hasTitleField: false
icon: bars
name: 'Menu Column'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Menu Column: { itemTitle }'
titleTranslationKeyFormat: null
titleTranslationMethod: site
