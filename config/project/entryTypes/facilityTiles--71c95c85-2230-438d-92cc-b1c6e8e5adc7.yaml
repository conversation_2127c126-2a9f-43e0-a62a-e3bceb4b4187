color: cyan
fieldLayouts:
  862817e5-16f6-4f52-a74e-c7cbd73c02e1:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-24T05:32:33+00:00'
            elementCondition: null
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1a7b2a71-3356-4631-92fb-f2da5f0515cb
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-24T05:44:40+00:00'
            elementCondition: null
            fieldUid: d6d6da50-4547-461e-9b4d-dc4b2010ffb1 # Default
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: 'Show on applicable accommodation pages'
            type: craft\fieldlayoutelements\CustomField
            uid: 78e0e663-d18c-485a-b30d-e6d24f9c2155
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-28T02:39:32+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: d6d6da50-4547-461e-9b4d-dc4b2010ffb1 # Default
                  layoutElementUid: 78e0e663-d18c-485a-b30d-e6d24f9c2155
                  uid: 19fc49fa-eff0-4889-8429-bf84f8329de2
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 10e32fec-a630-438b-b7eb-78f77ca1766f # Slider or Grid
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: 'Grid mode is encouraged (exclusively) for the facilities listing page. This setting is not applicable when the facilities tiles are set to "default".'
            type: craft\fieldlayoutelements\CustomField
            uid: dcac93ec-db6b-412c-af2f-04b730506150
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-24T05:35:21+00:00'
            elementCondition: null
            fieldUid: 0d420372-a13e-4813-99fc-5fddc267fb34 # Limit
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 54c3a858-bc0c-4a29-a529-c92e9e86ea28
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-02-14T06:19:35+00:00'
            elementCondition: null
            fieldUid: cf0767df-b2de-4002-afc4-d08ee11fd923 # Enable Links
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b280cf92-0e45-4eec-be62-81d206373041
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-24T05:35:21+00:00'
            elementCondition: null
            fieldUid: 0c8bb2d5-c819-4e34-9857-bdcc8611cb2c # Facilities
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4089592f-c8f2-4532-ad06-1816d7942f7d
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 0a6d03cf-afa6-4978-8fba-29714bd79616
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-27T01:23:52+00:00'
            elementCondition: null
            fieldUid: 4cb26117-09e7-45e0-b133-7fb01223c980 # Section Header
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 90a322bb-5592-4942-af4f-9065ab36497d
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-27T01:23:52+00:00'
            elementCondition: null
            fieldUid: ec3238b0-5e12-4a47-8f6f-7d65e1de859f # Section Footer
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0e513c8d-d8e5-4fda-a432-dbdefbe5023b
            userCondition: null
            warning: null
            width: 100
        name: 'Header + Footer'
        uid: 03cbc3bb-1fb1-431a-aa6c-fb4862098ba4
        userCondition: null
handle: facilityTiles
hasTitleField: false
icon: plus
name: 'Facility Tiles'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Facility Tiles{ itemTitle ? '': '' ~ itemTitle }{ sliderOrGrid ? '' [grid]'' }'
titleTranslationKeyFormat: null
titleTranslationMethod: site
