color: emerald
fieldLayouts:
  11aef7ae-6eb9-4dec-bddb-6a357b6bff1a:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-09-05T02:45:28+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 7d9a3340-36bd-4491-b5fe-3f5baf2e680c
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-05T02:45:28+00:00'
            elementCondition: null
            fieldUid: 744bfd72-7a02-4488-8ab1-4968865a2744 # Window Content
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ede4bf2f-c9f4-4567-9e20-62975972022c
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-05T02:45:28+00:00'
            elementCondition: null
            fieldUid: 55661050-1f72-4312-b11f-bc1556cf56ab # Layout
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3792b1fe-6706-47b9-bd7d-3571501da4fe
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-05T02:45:28+00:00'
            elementCondition: null
            fieldUid: 5ac0236d-025c-469b-9e6c-8c2a061656ad # Extra content close button
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8aaa1340-e98a-4bf1-a3d4-9278632ca0f8
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-05T02:45:28+00:00'
            elementCondition: null
            fieldUid: 715df3c6-7270-412f-9093-b8ebe0e7342f # Display when exit intent detection
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2ae83e99-a3f2-485c-b9ad-584e0a500ce8
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-05T02:45:28+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: 715df3c6-7270-412f-9093-b8ebe0e7342f # Display when exit intent detection
                  layoutElementUid: 2ae83e99-a3f2-485c-b9ad-584e0a500ce8
                  uid: 66206df0-a873-4585-a87f-a7b099a2b428
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 66173656-654f-4f80-ad48-d41b71711f1f # Delay in seconds after page load
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b70ac034-7eb1-4377-a611-09252b5c908a
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-09-05T02:45:28+00:00'
            elementCondition: null
            fieldUid: d7352d39-aba5-4bc8-8168-2e7ade584eec # Days to wait to show again
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d398648e-9d17-4f7c-8b30-6e31dc9a3ad6
            userCondition: null
            warning: null
            width: 25
        name: Content
        uid: 9ca8eecd-2e0f-4d50-922c-849e951ed510
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-09-05T02:45:28+00:00'
            elementCondition: null
            fieldUid: 9ff82229-6197-484b-bc7f-d51bb8ba9196 # Show on all Pages
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e3f9c94d-6e20-4c1e-9408-4019b604f878
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-05T02:45:28+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: 9ff82229-6197-484b-bc7f-d51bb8ba9196 # Show on all Pages
                  uid: 2ac36453-1771-4a6c-95ec-532745449a3f
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 8a535a21-e012-44f9-98c8-eb4104104b01 # Show on Homepage
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 44cc5ee9-deac-4b0e-b4fd-1db8b8dd3609
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-05T02:45:28+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: 9ff82229-6197-484b-bc7f-d51bb8ba9196 # Show on all Pages
                  uid: 8b4ebeb6-1287-4401-986a-d43d1413e675
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: e02b242d-8d5a-4693-a7a4-deddaee5bfb0 # Display URLs
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8985e2e6-43b3-49de-9f4f-b436a9c2f952
            userCondition: null
            warning: null
            width: 100
        name: 'Page Display Rules'
        uid: 3aa98190-98cc-4a95-a8ef-ebe9bc388fae
        userCondition: null
handle: modalWindow
hasTitleField: true
icon: triangle-exclamation
name: 'Modal Window'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: ''
titleTranslationKeyFormat: null
titleTranslationMethod: site
