color: emerald
fieldLayouts:
  89b6d6bb-b33e-4a99-83b2-bcc7a40002d2:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-09-03T06:37:22+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            requirable: false
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 86559c1f-7b67-4acf-8ac8-337a2a3f845a
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-03T06:39:36+00:00'
            elementCondition: null
            fieldUid: 5d3725da-5409-4f3e-bef1-d987f8529903 # Form
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b266671e-e0a4-4d9c-9c41-b6c799691e27
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: f172ec12-5c8a-4acf-9c58-7f3038d4d357
        userCondition: null
handle: form
hasTitleField: false
icon: input-text
name: Form
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: '{form.one.title}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
