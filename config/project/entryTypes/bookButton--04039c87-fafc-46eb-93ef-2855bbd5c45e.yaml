color: blue
fieldLayouts:
  db465680-1a43-46da-b4c0-2a10b4a967a1:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-22T05:06:15+00:00'
            dismissible: false
            elementCondition: null
            style: tip
            tip: "Set a link to a booking portal to navigate to the portal immediately. This will prevent users from searching on the site in favour of a third-party portal. \r\nIf the link field is empty, the book button will show/hide the search bar, allowing users to search on the site."
            type: craft\fieldlayoutelements\Tip
            uid: 7e90f5b8-960a-4daa-b697-83d4154425b7
            userCondition: null
          -
            dateAdded: '2025-01-22T05:06:15+00:00'
            elementCondition: null
            fieldUid: d6f4d67f-350d-4c39-a6d7-56a5f5f001af # Link
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 37b71c41-241b-4260-b4ab-a3de1e081c75
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: ee574f65-a870-4aef-a0a0-6ee65fe1f00e
        userCondition: null
handle: bookButton
hasTitleField: false
icon: magnifying-glass
name: 'Book Button'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Book Button'
titleTranslationKeyFormat: null
titleTranslationMethod: site
