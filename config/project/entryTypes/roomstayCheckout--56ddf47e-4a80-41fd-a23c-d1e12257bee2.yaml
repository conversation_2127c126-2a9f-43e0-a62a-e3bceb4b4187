color: sky
fieldLayouts:
  b2e6ce94-79b1-4207-acad-7c76305f974b:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            dismissible: false
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\EmptyFieldConditionRule
                  fieldUid: fc2a3f5e-eb9b-49a9-ba03-6fa3e62dd843 # Redirect
                  operator: notempty
                  uid: efed5b30-553a-4437-92be-0b01121770f4
              elementType: craft\elements\Entry
              fieldContext: global
            style: warning
            tip: "This page is currently a redirect. \r\nYou can edit or remove the redirect under the *Settings* tab."
            type: craft\fieldlayoutelements\Tip
            uid: c3b07d46-6b45-45df-8abe-470068763af5
            userCondition: null
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-08-09T01:30:58+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: Title
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: ad475212-e149-43b7-9c6e-538d0ce0b4a2
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-21T04:42:21+00:00'
            elementCondition: null
            fieldUid: 692981e9-ded2-4931-bee6-40070fe65c66 # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5042799e-305f-4181-8c19-66fcff6355f7
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-21T04:41:57+00:00'
            elementCondition: null
            fieldUid: 692981e9-ded2-4931-bee6-40070fe65c66 # Subtitle
            handle: tagline
            includeInCards: false
            instructions: null
            label: Tagline
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8d5d0302-dc37-45ae-bd7e-9778eff1f40a
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-12-27T04:41:00+00:00'
            elementCondition: null
            fieldUid: 1f271e29-cf3d-46a5-9ad9-c04ccf0fd94a # Image
            handle: null
            includeInCards: false
            instructions: null
            label: 'Gallery Thumbnail'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c94aee1a-6fba-4532-9ac1-da36edbeea28
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: 0adc5399-0e40-4d46-9443-a85046d2751d # Page Content
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6c619bdd-cc1c-4840-98a9-4ca827759cda
            userCondition: null
            warning: null
            width: 100
        name: Pages
        uid: 8e473750-19c1-4db2-890e-a40cd26b8264
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-02-13T23:09:51+00:00'
            elementCondition: null
            fieldUid: da66d2a6-d308-4a4b-9afa-79e244997c0a # Use Custom Prefooter
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f39d909b-df65-49b8-a570-5084a52ca14e
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-02-13T23:07:10+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: da66d2a6-d308-4a4b-9afa-79e244997c0a # Use Custom Prefooter
                  layoutElementUid: 0832bf98-e79b-425f-84fb-3aeb2bace690
                  uid: 7a121524-017e-463d-9c8b-11faeaad0cb6
                  value: true
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 7e433a91-21a2-4de6-93bc-5278e47d6fc6 # Custom Prefooter
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a2160867-7455-48bf-9423-b15c8ceb345b
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-02-13T23:09:51+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: 99dd7619-bc31-4fba-839a-a4b498d0e0fe
            userCondition: null
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: ed08dded-d02c-4a15-a218-b5e7b8370a35 # Enable Sidebar
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 33a176d7-6ade-4c4b-9a6c-9e156dcc6e63
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: ed08dded-d02c-4a15-a218-b5e7b8370a35 # Enable Sidebar
                  uid: 568e3c33-de0b-4de6-858d-f08287bb9345
                  value: true
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 98c2e58b-0203-4aa4-89af-4036d893dfca # Sidebar Addons
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 37b85c62-02e9-4fa5-a619-d647e792e798
            userCondition: null
            warning: null
            width: 50
        name: Layout
        uid: a9c56750-47af-4e64-a56a-367b916a57b6
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: 4ed4074e-eb71-49d9-b9a9-05f308dcd29a # Navigation Title
            handle: null
            includeInCards: false
            instructions: null
            label: 'Navigation Title'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: da369804-db57-426d-b7d0-c4c4559c2db6
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: 2f4881f2-619d-484d-8bae-035941bc9724 # Navigation Visibility
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6f98bbf0-7d03-43cf-9dc5-1b3fb443d983
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            heading: Meta
            type: craft\fieldlayoutelements\Heading
            uid: 66bfc03d-beaa-4bf1-b6a0-76236d011e60
            userCondition: null
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: afb06c07-eee2-4c2f-a623-7c5976c82755 # Meta Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 867bca6e-60dc-40c2-b380-03cbdff4ed5e
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: f85a92fb-60aa-4278-84f7-0e9c0d7765f2 # Meta Description
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0dc8bfc5-1bf5-4ae0-83f7-fc23d43c7ec4
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: 771d90ed-3e58-4e4b-8340-54e0af52cc39 # Meta Keywords
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1c6800c7-d3d3-4404-b6a6-5208bdb495dc
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-14T02:34:07+00:00'
            elementCondition: null
            fieldUid: 228d33d7-3ea6-427d-947d-02bf8fa66bfd # Meta Share Image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a239d8ef-e9a4-456d-ad17-5c8498c06acb
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:32:22+00:00'
            elementCondition: null
            fieldUid: 6c842807-a0bf-4772-9fac-7ac0332c105f # Canonical Url
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: fb36b651-2cb1-49c9-8c09-9a478d12956d
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            heading: Redirect
            type: craft\fieldlayoutelements\Heading
            uid: 3b664373-b6e1-44d0-a996-a3beba108225
            userCondition: null
          -
            dateAdded: '2024-08-09T01:30:58+00:00'
            elementCondition: null
            fieldUid: fc2a3f5e-eb9b-49a9-ba03-6fa3e62dd843 # Redirect
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4e9e865b-b153-4d82-8dc1-cef2f88a6b2a
            userCondition: null
            warning: null
            width: 100
        name: Settings
        uid: 81b3ca2d-f0c4-4928-8a07-3a3877c93190
        userCondition: null
handle: roomstayCheckout
hasTitleField: true
icon: cart-shopping
name: 'Roomstay Checkout'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: '{title}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
