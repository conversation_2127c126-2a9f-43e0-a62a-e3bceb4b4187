color: null
fieldLayouts:
  0dff921d-de08-4aa1-8ece-cc550b003b55:
    cardView:
      - 'layoutElement:99ec48f5-ebf6-4c29-a958-5b35569ad685'
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-22T04:34:43+00:00'
            elementCondition: null
            fieldUid: 06bbbbc1-a324-4d8f-a7ab-35ffdec4e3ad # Gap size
            handle: null
            includeInCards: true
            instructions: null
            label: 'Gap size'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 99ec48f5-ebf6-4c29-a958-5b35569ad685
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-22T04:34:43+00:00'
            elementCondition: null
            fieldUid: 465145dd-3bd1-4710-927e-28ed6b4aca6e # Background Colour
            handle: null
            includeInCards: false
            instructions: null
            label: 'Background Colour'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ceb751fe-d505-447a-bbe5-ccb0728e6fe2
            userCondition: null
            warning: null
            width: 50
        name: Content
        uid: fa7f996a-c84e-4a96-8ddb-325cc4ca75df
        userCondition: null
handle: verticalGap
hasTitleField: false
icon: cube
name: 'Vertical Gap'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
