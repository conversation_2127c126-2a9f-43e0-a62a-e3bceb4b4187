color: blue
fieldLayouts:
  5ac66eee-f180-4f7a-8aa3-21f4d7dd3a9e:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-27T00:49:26+00:00'
            elementCondition: null
            fieldUid: 58a0197d-774d-40de-b9a3-3e9dceccd8c1 # Richtext — Basic + Links
            handle: notes
            includeInCards: false
            instructions: null
            label: Notes
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5b56467d-f7a0-4e92-bd3f-3fa0c5d92370
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-27T00:49:26+00:00'
            elementCondition: null
            fieldUid: ca688c53-4fa3-4026-a0bb-4d86bb5ac683 # Link Helper
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 76d6561d-89a3-42fa-abe2-142891bf7a09
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 48601960-c186-42ce-b126-ecadd6375663
        userCondition: null
handle: sectionFooter
hasTitleField: false
icon: heading
name: 'Section Footer'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Section Footer: {{ linkHelper.one().linkField.label ?? null }}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
