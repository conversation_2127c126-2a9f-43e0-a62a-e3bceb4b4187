color: cyan
fieldLayouts:
  121863eb-e13a-4774-89be-1ab3bab4949e:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-02-05T00:55:29+00:00'
            elementCondition: null
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4886776e-f77e-4c9c-a208-e29a684cca67
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-02-05T01:01:46+00:00'
            elementCondition: null
            fieldUid: 03e146c1-9105-485d-92c9-8b6e8d62449a # Display Events By
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d82ae008-e659-48f5-b9fd-38c77b26a4d9
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: da9fb6be-f702-48cf-95cd-f92accd32c6c
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-27T01:23:52+00:00'
            elementCondition: null
            fieldUid: 4cb26117-09e7-45e0-b133-7fb01223c980 # Section Header
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 90a322bb-5592-4942-af4f-9065ab36497d
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-27T01:23:52+00:00'
            elementCondition: null
            fieldUid: ec3238b0-5e12-4a47-8f6f-7d65e1de859f # Section Footer
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0e513c8d-d8e5-4fda-a432-dbdefbe5023b
            userCondition: null
            warning: null
            width: 100
        name: 'Header + Footer'
        uid: 03cbc3bb-1fb1-431a-aa6c-fb4862098ba4
        userCondition: null
handle: eventsGallery
hasTitleField: false
icon: plus
name: 'Events Gallery'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Events by { displayEventsBy ? ''Calendar'' : ''List'' }'
titleTranslationKeyFormat: null
titleTranslationMethod: site
