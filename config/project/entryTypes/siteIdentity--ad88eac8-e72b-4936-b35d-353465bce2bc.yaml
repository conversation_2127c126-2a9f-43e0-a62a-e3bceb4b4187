color: null
fieldLayouts:
  3769afde-eb67-489a-98d0-66421a951557:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-01-03T05:12:10+00:00'
            elementCondition: null
            fieldUid: 23c6d4e9-ef9c-4b90-9f9e-8a4f7eb2215c # Logo
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: "For best results please upload both a vector `.svg` file and a rasterised `.png` file. \r\nAt minimum a `.png` file is needed."
            type: craft\fieldlayoutelements\CustomField
            uid: bc63218d-50fd-428b-be76-172f987e60df
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-16T03:47:27+00:00'
            elementCondition: null
            fieldUid: 5610df98-bec1-4ad3-b30e-cc2b4f78c554 # Favicon
            handle: favicon
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: "Please upload 3 files: `.png`, `.ico` and `.svg`. \r\nAll images must be square, and ensure the `.png` image is at least 512px by 512px.\r\nYou can use [realfavicongenerator.net](https://realfavicongenerator.net/) to help generate your files. \r\nConsider making the favicon as simplistic and minimalist as possible; it will be shrunk to 16px by 16px (at its smallest) and any detail will be lost."
            type: craft\fieldlayoutelements\CustomField
            uid: 17e9f901-4db1-45b0-9b50-a0e3de625522
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-01-03T05:12:10+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\LineBreak
            uid: 5ba4558d-29d6-4dc4-aa81-67eab37b5df6
            userCondition: null
          -
            dateAdded: '2025-01-03T05:28:36+00:00'
            elementCondition: null
            fieldUid: b8be04bf-f98b-4b09-a77e-85718c95ea19 # Affiliate Logo Position
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 70365a58-b6d1-4504-872b-9d1e5014b2ff
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-03T05:12:10+00:00'
            elementCondition: null
            fieldUid: 23c6d4e9-ef9c-4b90-9f9e-8a4f7eb2215c # Logo
            handle: affiliateLogo
            includeInCards: false
            instructions: null
            label: 'Affiliate Logo'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1ce4f289-5d3d-427c-97cf-a4c43c76c73d
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-03T05:12:10+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\LineBreak
            uid: b83f8bb2-4b03-42a9-8133-1c93c82e49db
            userCondition: null
          -
            dateAdded: '2025-01-03T05:12:10+00:00'
            elementCondition: null
            fieldUid: 84f80418-1700-4a6e-b032-dd0a2e91baee # Brand Colour
            handle: primaryColour
            includeInCards: false
            instructions: null
            label: 'Primary Colour'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6e621b9f-2cb5-4dcc-a619-27c554b50237
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-03T05:12:10+00:00'
            elementCondition: null
            fieldUid: 84f80418-1700-4a6e-b032-dd0a2e91baee # Brand Colour
            handle: primaryColourWCAG
            includeInCards: false
            instructions: null
            label: 'Primary Colour WCAG'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7a061ff4-a9b7-4854-8b49-5b8a7ec8e0d3
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-03T05:12:10+00:00'
            elementCondition: null
            fieldUid: 84f80418-1700-4a6e-b032-dd0a2e91baee # Brand Colour
            handle: primaryColourAlt
            includeInCards: false
            instructions: null
            label: 'Primary Colour Alt'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1f3d14f7-1ebf-4cef-8538-31e4314f69e8
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-03T05:12:10+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\LineBreak
            uid: 7ac9b9ba-4e70-48e5-b19f-8b0d0e50c714
            userCondition: null
          -
            dateAdded: '2025-01-03T05:12:10+00:00'
            elementCondition: null
            fieldUid: 84f80418-1700-4a6e-b032-dd0a2e91baee # Brand Colour
            handle: accentColour
            includeInCards: false
            instructions: null
            label: 'Accent Colour'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 55bd2908-95e4-4499-a0fd-79c8f6254c16
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-03T05:12:10+00:00'
            elementCondition: null
            fieldUid: 84f80418-1700-4a6e-b032-dd0a2e91baee # Brand Colour
            handle: accentColourWCAG
            includeInCards: false
            instructions: null
            label: 'Accent Colour WCAG'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d74e4f84-edf7-412d-99f4-f210cbf4a1b4
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-03T05:12:10+00:00'
            elementCondition: null
            fieldUid: 84f80418-1700-4a6e-b032-dd0a2e91baee # Brand Colour
            handle: accentColourAlt
            includeInCards: false
            instructions: null
            label: 'Accent Colour Alt'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 80a57b9a-f4ef-4017-b824-c0ec13890c7c
            userCondition: null
            warning: null
            width: 25
        name: Brand
        uid: 5c2c3685-267a-42b6-9cac-40ee7b8a97d6
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: 6bf96f07-6131-4c8b-80e2-51bcb05f3a02 # Company Name
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 917f30df-f063-41cb-8b6e-f0a5afc84389
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: *************-4ff1-892e-df6def007821 # ABN
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 73f56420-9e48-4817-af32-81975f3775bf
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: 179f1561-ce06-4387-bbdf-208df33bc851 # Email Address
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d6af86d7-7d1f-4ae2-9e67-1bc3a8815ce6
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: 34344df6-73da-4403-afd2-beda8a573d0c # Phone Number
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ea5d8f57-3b16-49b4-b1c6-370b7454bc76
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: b5f1c7b2-2601-4f1e-996f-1fbbadb7e646 # Address
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8d52c61e-a5ba-4d25-80ac-f13a34909b5c
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-01-16T23:26:00+00:00'
            elementCondition: null
            fieldUid: d62e9ec1-b0e8-4309-8388-d81a3e93821d # Latitude
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 97fdcd54-99ef-4d0b-b443-46fa1c6a416f
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-16T23:26:00+00:00'
            elementCondition: null
            fieldUid: ad9c3f1a-b3f2-4e9d-ae36-913e468fd6d0 # Longitude
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2e6be492-916e-4e26-a9f7-fda3555cf78b
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-17T00:39:53+00:00'
            elementCondition: null
            fieldUid: e74b367f-00bb-4e22-bf47-257429d4dd0e # Google Map Link
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: be2b3fab-8a3e-4dba-8b06-5cdf47479966
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-17T00:39:53+00:00'
            elementCondition: null
            fieldUid: 0cd3b86e-1343-4a02-848f-0f38d7a6552a # Google Map Embed
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6e5334bf-e63c-4b91-bb09-ab43b069ddaa
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-16T23:47:11+00:00'
            elementCondition: null
            fieldUid: 1f271e29-cf3d-46a5-9ad9-c04ccf0fd94a # Image
            handle: mapMarker
            includeInCards: false
            instructions: null
            label: 'Map Marker'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a5b9d1a0-7ee3-42cd-bbef-2df6e75a35db
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: b7b5f77c-5177-4e61-9ab9-726ddc6a8d6e # Operating Hours
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 99fb4143-9f89-4369-a68d-56f17cff48a6
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-02-03T01:54:08+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: faf83bd5-b946-44de-9b18-2b8a48a55f4e
            userCondition: null
          -
            dateAdded: '2025-02-03T01:54:08+00:00'
            elementCondition: null
            fieldUid: 231f5465-02a7-4ca0-b67b-8392551db2b1 # Subscribe Tag
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0676a8b0-7007-4cbb-a9dc-bb0760393415
            userCondition: null
            warning: 'If you don''t explicitly set a tag, the tag will be automatically generated and may not match the data in your newsletter management software.'
            width: 100
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: 9d34c00e-dfbb-4fe9-ac1d-8ab4a48296f4
            userCondition: null
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: 2f4c6551-4431-4352-8c81-6989358d2bbc # Image List
            handle: null
            includeInCards: false
            instructions: 'A selection of images that are okay to be randomly selected for assets that don''t have an image set but are deemed better with an image.'
            label: 'Fallback Images'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 75591404-f20b-4eaa-a013-f73ffd2e2162
            userCondition: null
            warning: null
            width: 100
        name: 'Contact Information'
        uid: 1eb2a4a7-39c7-4152-ad3d-f33e213abc50
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: 2b31ecb8-c987-4347-944b-100d09b2e876 # Default Social Media image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b48fe897-d02b-45a4-8d5d-58366784f7c9
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: 4b044761-5d09-4c4a-9d7d-8e0d1082c639 # Facebook URL
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 63e3acdb-27d0-47fc-b661-4ed733056200
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: 69b88b66-79dc-4aa2-b451-99b1a8664d18 # Instagram URL
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 66fc2e71-ab46-46d9-b434-dff99875c7c3
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: 892ed559-6744-4a00-b468-90914932245d # LinkedIn URL
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: eb0949e2-bd63-4271-b875-3a3b7ad0ff01
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: 4d62c876-d02d-486f-8f5f-5f45aa416d97 # TikTok URL
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1b5c985f-944c-4feb-8dad-51d6fed59316
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: 12964fb8-f40f-4aed-920d-70219f4e290c # X/Twitter URL
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 96155871-cea0-441a-b3e4-2ccc6c3bc6fe
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: 902c8081-5f52-4843-b998-d0808ffc039b # Twitter Handle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 31341c33-dd1e-442a-81f8-42dea4f18afc
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: ed8655a6-ecee-4828-b2f8-29272c76d528 # YouTube URL
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a2055dec-1612-4684-8df6-ea3aacd900cb
            userCondition: null
            warning: null
            width: 100
        name: Socials
        uid: 2333ac55-ff31-4bde-85cc-bf6487d149e2
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-18T22:43:47+00:00'
            elementCondition: null
            fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
            handle: postsParentPage
            includeInCards: false
            instructions: null
            label: 'Posts Parent Page'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f150881e-488f-4f4d-8a1a-8e9002d07485
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-24T00:52:36+00:00'
            elementCondition: null
            fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
            handle: attractionsParentPage
            includeInCards: false
            instructions: null
            label: 'Attractions Parent Page'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 72749729-7ba1-402e-bb42-ddc747d6f08a
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-24T00:50:46+00:00'
            elementCondition: null
            fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
            handle: facilitiesParentPage
            includeInCards: false
            instructions: null
            label: 'Facilities Parent Page'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1c44b085-ea30-4ca1-b99e-1a7835106658
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-24T00:50:46+00:00'
            elementCondition: null
            fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
            handle: dealsParentPage
            includeInCards: false
            instructions: null
            label: 'Deals Parent Page'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f0227175-5a1c-42ba-98a3-7caec6b0dd20
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-01-15T03:55:38+00:00'
            elementCondition: null
            fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
            handle: testimonialsParentPage
            includeInCards: false
            instructions: null
            label: 'Testimonials Parent Page'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 78275fdd-3eae-4bd4-86ed-fff0e0dee8e6
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-02-05T23:07:10+00:00'
            elementCondition: null
            fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
            handle: eventsParentPage
            includeInCards: false
            instructions: null
            label: 'Events Parent Page'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ab379985-b36b-45b6-9f2a-b23299f4b012
            userCondition: null
            warning: null
            width: 25
        name: 'Collection Parent Pages'
        uid: b38af935-9355-4372-a43a-2de0bce34801
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-02-17T03:58:15+00:00'
            elementCondition: null
            fieldUid: 1bc02563-f940-42c1-b38f-6358d4cb3e3d # Target Entry
            handle: homePageEntry
            includeInCards: false
            instructions: 'Use this page as the home page'
            label: 'Home Page Override'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7864933f-44a7-4496-911a-5d64014161bc
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-02-05T05:00:47+00:00'
            elementCondition: null
            fieldUid: ac47dfe7-5f87-4a4e-bc85-7371a5794be4 # Require Admin Login
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2bfbe37a-d846-4357-9dbb-0bb264cce2e2
            userCondition: null
            warning: 'Toggling this on will hide the entire website behind a login wall and only logged-in admin users can view the site.'
            width: 100
          -
            dateAdded: '2025-02-14T05:54:43+00:00'
            elementCondition: null
            fieldUid: 90e2d881-901f-431c-a789-6db0e8d3d267 # Show Header
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d44d4493-0991-4924-8bee-028c54d700f6
            userCondition: null
            warning: null
            width: 100
        name: Configuration
        uid: 2a68d317-f11f-4758-89ea-23cd2cb98c94
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-09-17T02:29:07+00:00'
            elementCondition: null
            fieldUid: bce133b8-d726-4c05-a7c2-9353d45e3b86 # Richtext — Standard
            handle: null
            includeInCards: false
            instructions: 'Override default 404 error page text.'
            label: '404 error message'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8ab7f124-969d-4e42-9149-5a87e45da493
            userCondition: null
            warning: null
            width: 100
        name: '404'
        uid: 19c98d32-441f-4464-8003-ab4ad3ee104e
        userCondition: null
handle: siteIdentity
hasTitleField: false
icon: null
name: 'Site Identity'
showSlugField: true
showStatusField: false
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Site Identity'
titleTranslationKeyFormat: null
titleTranslationMethod: site
