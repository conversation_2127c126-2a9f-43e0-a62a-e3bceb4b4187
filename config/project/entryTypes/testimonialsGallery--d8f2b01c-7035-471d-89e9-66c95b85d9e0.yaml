color: cyan
fieldLayouts:
  75bc9184-c88a-4d51-a36f-bf70b1337032:
    tabs:
      -
        elementCondition: null
        elements:
          -
            content: "The Testimonial Gallery page add-on will display all testimonials in a grid. It is recommended for use on an *all reviews* or *all testimonials* page.  \r\nIf one creates an *all reviews* or *all testimonials* page, be sure to specify it as the *Testimonial Parent Page* under **Collection Parent Pages** within the *Site Identity*."
            dateAdded: '2025-02-12T23:36:00+00:00'
            displayInPane: true
            elementCondition: null
            type: craft\fieldlayoutelements\Markdown
            uid: 7676a421-6d73-4335-bf3f-3e5672202fd2
            userCondition: null
            width: 100
        name: Content
        uid: e8dea7d4-f99b-4f97-ad2a-1cd2727caf09
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-27T00:58:07+00:00'
            elementCondition: null
            fieldUid: 4cb26117-09e7-45e0-b133-7fb01223c980 # Section Header
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 89432ab5-73a6-4015-a8be-c12b397aa3c9
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-27T01:18:14+00:00'
            elementCondition: null
            fieldUid: ec3238b0-5e12-4a47-8f6f-7d65e1de859f # Section Footer
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bb7bf9dd-4523-48ee-a396-858c1c7416c7
            userCondition: null
            warning: null
            width: 100
        name: 'Header & Footer'
        uid: 301d6a85-b044-4150-bd6d-6039d6f48962
        userCondition: null
handle: testimonialsGallery
hasTitleField: false
icon: plus
name: 'Testimonials Gallery'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Testimonials Gallery{{ sectionHeader.one() ?? false ? '''': '''' ~ sectionHeader.one().itemTitle }}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
