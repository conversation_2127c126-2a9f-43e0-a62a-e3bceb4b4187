columnSuffix: null
handle: targetFile
instructions: null
name: 'Target File'
searchable: false
settings:
  allowSelfRelations: false
  allowSubfolders: false
  allowUploads: true
  allowedKinds: null
  branchLimit: null
  defaultUploadLocationSource: 'volume:8b18b1ba-b9ba-4308-b4f8-bec8c72b9a42' # Documents
  defaultUploadLocationSubpath: '{{ site.name|lower }}{{ type == ''accommodation'' ? ''/'' ~ slug }}'
  maintainHierarchy: false
  maxRelations: null
  minRelations: null
  previewMode: full
  restrictFiles: false
  restrictLocation: false
  restrictedDefaultUploadSubpath: null
  restrictedLocationSource: 'volume:623db196-498d-4c4b-ab91-8d16301180f3' # Images
  restrictedLocationSubpath: null
  selectionLabel: null
  showCardsInGrid: false
  showSiteMenu: true
  showUnpermittedFiles: false
  showUnpermittedVolumes: false
  sources: '*'
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: none
type: craft\fields\Assets
