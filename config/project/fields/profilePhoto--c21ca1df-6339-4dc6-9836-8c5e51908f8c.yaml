columnSuffix: null
handle: profilePhoto
instructions: null
name: 'Profile photo'
searchable: false
settings:
  allowSelfRelations: false
  allowSubfolders: false
  allowUploads: true
  allowedKinds: null
  branchLimit: null
  defaultUploadLocationSource: 'volume:623db196-498d-4c4b-ab91-8d16301180f3' # Images
  defaultUploadLocationSubpath: '{{ site.name|lower }}{{ type == ''accommodation'' ? ''/'' ~ slug }}/team-members'
  maintainHierarchy: false
  maxRelations: 1
  minRelations: null
  previewMode: full
  restrictFiles: false
  restrictLocation: false
  restrictedDefaultUploadSubpath: null
  restrictedLocationSource: 'volume:623db196-498d-4c4b-ab91-8d16301180f3' # Images
  restrictedLocationSubpath: null
  selectionLabel: null
  showCardsInGrid: false
  showSiteMenu: false
  showUnpermittedFiles: false
  showUnpermittedVolumes: false
  sources:
    - 'volume:623db196-498d-4c4b-ab91-8d16301180f3' # Images
  targetSiteId: null
  validateRelatedElements: false
  viewMode: large
translationKeyFormat: null
translationMethod: none
type: craft\fields\Assets
