columnSuffix: null
handle: postCategory
instructions: null
name: 'Post Category'
searchable: true
settings:
  allowSelfRelations: false
  branchLimit: null
  maintainHierarchy: false
  maxRelations: null
  minRelations: null
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Entry
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\entries\EntryCondition
      -
        - conditionRules
        -
          -
            __assoc__:
              -
                - class
                - craft\elements\conditions\entries\TypeConditionRule
              -
                - uid
                - 414a3e2b-711d-41d8-a220-a9c2f419e9a2
              -
                - operator
                - in
              -
                - values
                -
                  - 1c06507e-4d8f-4f58-b6c5-a3c82a03041e # Post Category
  selectionLabel: null
  showCardsInGrid: false
  showSiteMenu: true
  sources:
    - 'section:c345d6df-715a-475d-b0b3-f48a8d61099d' # Categories
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: none
type: craft\fields\Entries
