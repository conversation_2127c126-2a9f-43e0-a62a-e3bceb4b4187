columnSuffix: null
handle: systemIcon
instructions: null
name: 'System Icon'
searchable: false
settings:
  allowSelfRelations: false
  allowSubfolders: false
  allowUploads: true
  allowedKinds:
    - image
  branchLimit: null
  defaultUploadLocationSource: 'volume:89e7e7df-e72c-4b4d-9d61-1d8d8ac63698' # Icons
  defaultUploadLocationSubpath: system
  maintainHierarchy: false
  maxRelations: 1
  minRelations: null
  previewMode: full
  restrictFiles: true
  restrictLocation: true
  restrictedDefaultUploadSubpath: null
  restrictedLocationSource: 'volume:89e7e7df-e72c-4b4d-9d61-1d8d8ac63698' # Icons
  restrictedLocationSubpath: '{{ site.name|lower }}{{ type == ''accommodation'' ? ''/'' ~ slug }}/system'
  selectionLabel: 'Add an icon'
  showCardsInGrid: false
  showSiteMenu: true
  showUnpermittedFiles: false
  showUnpermittedVolumes: false
  sources:
    - 'volume:89e7e7df-e72c-4b4d-9d61-1d8d8ac63698' # Icons
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: none
type: craft\fields\Assets
