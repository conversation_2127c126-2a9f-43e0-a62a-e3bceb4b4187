columnSuffix: null
handle: setAccommodationGroup
instructions: null
name: 'Set Accommodation Group'
searchable: false
settings:
  allowSelfRelations: false
  branchLimit: null
  maintainHierarchy: false
  maxRelations: null
  minRelations: null
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Entry
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\entries\EntryCondition
      -
        - conditionRules
        -
          -
            __assoc__:
              -
                - class
                - craft\elements\conditions\entries\TypeConditionRule
              -
                - uid
                - bbff6aa0-5653-4419-b27b-6966334ef97b
              -
                - operator
                - in
              -
                - values
                -
                  - 9e3c6f53-9b5d-4aa5-b2be-1be2e28ab519 # Accommodation Group
  selectionLabel: 'Set type'
  showCardsInGrid: false
  showSiteMenu: false
  sources:
    - 'section:8e022768-bf63-43c1-ad96-2f193cd7f337' # Pages
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: none
type: craft\fields\Entries
