columnSuffix: null
handle: pageContent
instructions: 'Build up your desired page content'
name: 'Page Content'
searchable: true
settings:
  createButtonLabel: 'Add Content'
  defaultIndexViewMode: cards
  entryTypes:
    -
      __assoc__:
        -
          - uid
          - ee318474-ed6f-4ce5-8d21-b9f8dc1c8316 # Richtext
    -
      __assoc__:
        -
          - uid
          - c22fd2c3-2678-4e83-ac52-7bc7d6db3af0 # Panel Layout
    -
      __assoc__:
        -
          - uid
          - 55507305-f580-44dd-a4f5-6657b10c4ffc # Page Add-on
    -
      __assoc__:
        -
          - uid
          - a75b53e1-12a1-41ad-8c87-897de6a53c69 # HTML / Widget
    -
      __assoc__:
        -
          - uid
          - 4a093217-dfb7-4f50-8c3d-14596c29e9bd # Vertical Gap
  includeTableView: false
  maxEntries: null
  minEntries: null
  pageSize: null
  propagationKeyFormat: null
  propagationMethod: all
  showCardsInGrid: false
  viewMode: blocks
translationKeyFormat: null
translationMethod: site
type: craft\fields\Matrix
