columnSuffix: null
handle: videoFile
instructions: null
name: 'Video File'
searchable: false
settings:
  allowSelfRelations: false
  allowSubfolders: false
  allowUploads: true
  allowedKinds:
    - video
  branchLimit: null
  defaultUploadLocationSource: 'volume:88da6c74-0b14-4006-8ce2-77db52c368e9' # Videos
  defaultUploadLocationSubpath: '{{ site.name|lower }}{{ type == ''accommodation'' ? ''/'' ~ slug }}'
  maintainHierarchy: false
  maxRelations: 1
  minRelations: null
  previewMode: full
  restrictFiles: true
  restrictLocation: false
  restrictedDefaultUploadSubpath: null
  restrictedLocationSource: 'volume:623db196-498d-4c4b-ab91-8d16301180f3' # Images
  restrictedLocationSubpath: null
  selectionLabel: null
  showCardsInGrid: false
  showSiteMenu: false
  showUnpermittedFiles: false
  showUnpermittedVolumes: false
  sources:
    - 'volume:88da6c74-0b14-4006-8ce2-77db52c368e9' # Videos
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: none
type: craft\fields\Assets
