columnSuffix: null
handle: singleAccommodation
instructions: null
name: 'Single Accommodation'
searchable: false
settings:
  allowSelfRelations: false
  branchLimit: null
  maintainHierarchy: false
  maxRelations: 1
  minRelations: null
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Entry
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\entries\EntryCondition
      -
        - conditionRules
        -
          -
            __assoc__:
              -
                - class
                - craft\elements\conditions\entries\TypeConditionRule
              -
                - uid
                - b124ba54-984d-4e8e-ae06-d268f18dba94
              -
                - operator
                - in
              -
                - values
                -
                  - 54f6fb24-c2fa-447a-8610-4faffcf52391 # Accommodation
  selectionLabel: null
  showCardsInGrid: false
  showSiteMenu: false
  sources:
    - 'section:8e022768-bf63-43c1-ad96-2f193cd7f337' # Pages
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: none
type: craft\fields\Entries
