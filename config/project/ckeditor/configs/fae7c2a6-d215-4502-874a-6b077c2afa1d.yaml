css: "/* .ck stops the class bleeding out the editor */\r\n.ck .button {\r\n    text-decoration: none;\r\n    padding: 8px 16px;\r\n    text-align: center;\r\n    display: inline-grid;\r\n    grid-auto-flow: column;\r\n    column-gap: 0.5em;\r\n    justify-content: center;\r\n    align-items: center;\r\n    border-radius: 4px;\r\n    position: relative;\r\n    margin: 0;\r\n    color: #ffffff;\r\n    background-color: #007AFF;\r\n}\r\n\r\n.ck .button.ck-link_selected {\r\n    background-color: #005FC6;\r\n}\r\n\r\n.ck .text-link {\r\n    display: inline-grid;\r\n    grid-auto-flow: column;\r\n    column-gap: 4px;\r\n    justify-content: start;\r\n    align-items: center;\r\n    color: var(--color-accent-wcag, #007AFF);\r\n    background: transparent;\r\n}\r\n\r\n.ck .text-link:after {\r\n    content: \"\";\r\n    display: block;\r\n    width: 7px;\r\n    height: 7px;\r\n    border-width: 0;\r\n    border-style: solid;\r\n    border-color: currentColor;\r\n    border-top-width: 1.5px;\r\n    border-right-width: 1.5px;\r\n    transform: rotate(45deg);\r\n}\r\n\r\n.ck .button.text-link {\r\n    color: #005FC6;\r\n}"
headingLevels:
  - 2
  - 3
  - 4
  - 5
  - 6
name: Default
options:
  alignment:
    options:
      - left
      - center
      - right
  code:
    indentSequence: '  '
  htmlSupport:
    allow:
      -
        classes: true
        name: div
  image:
    toolbar:
      - transformImage
      - 'imageStyle:inline'
      - 'imageStyle:block'
      - 'imageStyle:side'
      - 'imageStyle:wrapText'
      - 'imageStyle:breakText'
      - '|'
      - toggleImageCaption
      - imageTextAlternative
  link:
    decorators:
      openInNewTab:
        attributes:
          rel: 'noopener noreferrer'
          target: _blank
        label: 'Open in a new tab'
        mode: manual
  style:
    definitions:
      -
        classes:
          - button
        element: a
        name: Button
      -
        classes:
          - text-link
        element: a
        name: 'Text Link'
toolbar:
  - heading
  - '|'
  - style
  - '|'
  - blockQuote
  - bold
  - italic
  - insertTable
  - link
  - anchor
  - bulletedList
  - numberedList
  - alignment
  - insertImage
  - mediaEmbed
  - sourceEditing
  - findAndReplace
  - undo
  - createEntry
