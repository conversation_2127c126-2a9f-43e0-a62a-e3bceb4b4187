<!-- Template: {{ _self }} -->

{% set articleImage = articleItem.banner.one() ?? articleItem.imageSingular.one() ?? null %}
{% set articleType = articleItem.type.handle %}
{% set articleSubtitle = articleItem.subtitle ?? (articleType == 'attraction' ? 'Local Attraction' : articleItem.type.name) %}

<article class="article {{ modifierClass ?? null }}">
    <div class="article__content">

        <hgroup>
            <p class="article__subtitle">{{ articleSubtitle|capitalize }}</p>
            <h3 class="article__heading">{{ articleItem.itemTitle ?? articleItem.navigationTitle ?? articleItem.title }}</h3>
        </hgroup>

        <div class="article__description">
            {% if (articleType == 'article' and articleItem.richtextBasicLinks|length) or (articleType in ["accommodationGroup", "attraction"] and articleItem.richtextBasicLinks|length) %}
                {{ articleItem.richtextBasicLinks }}
            {% elseif articleItem.excerpt ?? null %}
                <p>{{ articleItem.excerpt }}</p>
            {% endif %}
        </div>

        {% if articleItem.linkHelper|length %}

            {% include './01_core/_blocks/link-helper.twig' with { 'modifierClass' : 'button article__cta' } %}

        {% elseif (articleItem.linkField ?? null) or (articleType != 'article' and articleItem.url ?? null) %}

            {% set link = articleItem.linkField ?? null %}
            {% if articleType != 'article' and articleItem.url ?? null %}
                {% set link = {
                    value: articleItem.url,
                    target: null,
                    label: linkLabelFallback ?? "Read more",
                } %}
            {% endif %}

            {% include '01_core/_blocks/individual-link' with {
                link: link,
                icon: staticIcon is defined ? staticIcon : null,
                modifierClass: 'article__cta',
                linkLabelFallback: linkLabelFallback ?? null,
                linkContextParent: 'article',
            } %}

        {% endif %}

    </div>

    {% if articleImage %}
        {{ articleImage.getImg({ width: 3200, height: 1800, format: 'webp', quality: 85 }, ['1.5x', '2x', '3x'])|attr({
                loading: 'lazy',
                class: 'article__image',
                role: articleImage.alt ? null : 'presentation',
            })
        }}
    {% endif %}

    {% if sliderCount is defined %}
    <div class="article__slider-bar">
        <span class="article__slider-bar__subtitle">{{ articleSubtitle }}</span>
        <span class="article__slider-bar__index">{{ loop.index }} of {{ sliderCount }}</span>
    </div>
    {% endif %}


</article>
